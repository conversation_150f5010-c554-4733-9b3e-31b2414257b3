# PLAN.MD
Read the plan and write code for the 3 libraries to extract the text from the pdf folder in each lib folder, name the text 123.md



Best and Newest Way to OCR Local PDF Files with Python (2025)
If you want to extract just the text (no tables, no images) from local PDF files—especially scanned PDFs—using Python, the latest and most effective solutions in 2025 are based on open-source OCR libraries. Here’s how to do it:

Top Python OCR Libraries for PDFs
Tesseract OCR (with pytesseract)

Most widely used, robust, and accurate open-source OCR engine.

Excellent for extracting text from scanned PDFs.

Maintained by Google, with frequent updates and strong community support.

PaddleOCR

Newer, deep-learning-based, very accurate, and fast.

Supports over 80 languages.

Lightweight and easy to integrate in Python workflows.

EasyOCR

Simple API, deep learning-based, supports many languages.

Good for quick prototyping and straightforward text extraction.

Recommended Workflow (No Tables, No Images)
1. Install Required Libraries
For Tesseract OCR:

bash
pip install pytesseract pdf2image pillow
sudo apt install tesseract-ocr
For PaddleOCR:

bash
pip install paddleocr paddlepaddle pdf2image pillow
2. Convert PDF Pages to Images
PDFs must be converted to images (one image per page) before OCR:

python
from pdf2image import convert_from_path

pages = convert_from_path('yourfile.pdf')
3. Extract Text from Each Page
Using Tesseract (pytesseract):

python
import pytesseract

all_text = ""
for page in pages:
    text = pytesseract.image_to_string(page)
    all_text += text + "\n"
print(all_text)
Using PaddleOCR:

python
from paddleocr import PaddleOCR

ocr = PaddleOCR(use_angle_cls=True, lang='en')
all_text = ""
for idx, page in enumerate(pages):
    page.save(f"page_{idx}.jpg")
    result = ocr.ocr(f"page_{idx}.jpg", cls=True)
    for line in result[0]:
        all_text += line[1][0] + "\n"
print(all_text)
Why These Methods?
Tesseract and PaddleOCR are the most accurate, up-to-date, and actively maintained open-source OCR engines in 2025.

Both are easy to use in Python and handle most scanned PDF layouts.

This process extracts only the text—no tables, no images, just plain text strings.

Tips
For best results, preprocess images (deskew, denoise) using OpenCV if your scans are low-quality.

If your PDFs are not scanned (i.e., they are "digital" PDFs), you can use PyPDF2 or similar libraries to extract text directly, without OCR.